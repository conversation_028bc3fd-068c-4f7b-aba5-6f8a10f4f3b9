/**
 * Integration tests for the Hybrid Lexical Parser
 * Tests complex scenarios, edge cases, and real-world usage patterns
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import {
  markdownToLexical,
  lexicalToMarkdown,
  initializeHybridParser,
  reset,
  configure,
  getStats,
} from '../../src/utils/lexical-parser/hybrid';

describe('Hybrid Parser Integration', () => {
  beforeEach(async () => {
    reset();
    await initializeHybridParser({
      options: {
        enableGhostFeatures: true,
        enableObsidianFeatures: true,
      },
    });
  });

  afterEach(() => {
    reset();
  });

  describe('Complex Document Scenarios', () => {
    it('should handle mixed Ghost and Obsidian features', async () => {
      const markdown = `# Mixed Content Document

This document contains both Ghost and Obsidian features.

> [!note]
> This is a Ghost callout with [[Obsidian Link]] inside.

## Math Section

Inline math: $E = mc^2$ and block math:

$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$

## Table with Links

| Feature | Platform | Link |
| --- | --- | --- |
| Callouts | Ghost | [[Ghost Features]] |
| Wikilinks | Obsidian | [[Obsidian Features]] |
| Math | Obsidian | $\\LaTeX$ |

[bookmark](https://example.com)
title: Example Bookmark
description: A bookmark in the middle of content`;

      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      
      // Verify all features are preserved
      expect(backToMarkdown.data).toContain('[!note]');
      expect(backToMarkdown.data).toContain('[[Obsidian Link]]');
      expect(backToMarkdown.data).toContain('$E = mc^2$');
      expect(backToMarkdown.data).toContain('$$');
      expect(backToMarkdown.data).toContain('| Feature | Platform | Link |');
      expect(backToMarkdown.data).toContain('[bookmark](https://example.com)');
    });

    it('should handle nested structures', async () => {
      const markdown = `# Nested Structures

- List item with [[wikilink]]
  - Nested item with $math$
  - Another nested item
    - Deep nesting with **bold** text

> [!warning]
> Callout with nested content:
> 
> 1. Ordered list in callout
> 2. Item with \`code\`
> 3. Item with [regular link](https://example.com)

| Table | With | Complex |
| --- | --- | --- |
| Cell with [[link]] | Cell with $x^2$ | Cell with **bold** |
| Another row | With \`code\` | And *italic* |`;

      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);

      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      
      // Verify nested structures are preserved
      expect(backToMarkdown.data).toContain('[[wikilink]]');
      expect(backToMarkdown.data).toContain('$math$');
      expect(backToMarkdown.data).toContain('[!warning]');
      expect(backToMarkdown.data).toContain('1. Ordered list');
      expect(backToMarkdown.data).toContain('[[link]]');
      expect(backToMarkdown.data).toContain('$x^2$');
    });

    it('should handle real-world blog post structure', async () => {
      const markdown = `# Understanding Quantum Computing

> [!info]
> This post explores the fundamentals of quantum computing and its applications.

## Introduction

Quantum computing represents a paradigm shift in computational power. Unlike classical computers that use bits, quantum computers use **quantum bits** or *qubits*.

## Mathematical Foundation

The state of a qubit can be represented as:

$$
|\\psi\\rangle = \\alpha|0\\rangle + \\beta|1\\rangle
$$

Where $\\alpha$ and $\\beta$ are complex amplitudes satisfying $|\\alpha|^2 + |\\beta|^2 = 1$.

## Key Concepts

| Concept | Classical | Quantum |
| :--- | :---: | :---: |
| Basic Unit | Bit | Qubit |
| States | 0 or 1 | Superposition |
| Operations | Logic Gates | Quantum Gates |

For more details, see [[Quantum Mechanics Basics]] and [[Linear Algebra for QC]].

## Applications

1. **Cryptography**: Breaking RSA encryption
2. **Optimization**: Solving complex optimization problems
3. **Simulation**: Modeling quantum systems

> [!warning]
> Current quantum computers are still experimental and face significant challenges with **quantum decoherence**.

## Further Reading

[bookmark](https://quantum-computing.ibm.com)
title: IBM Quantum Computing
description: Learn about IBM's quantum computing platform
image: https://quantum-computing.ibm.com/images/quantum.jpg

The field is rapidly evolving, with new breakthroughs in [[Quantum Error Correction]] and [[Quantum Algorithms]].`;

      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      
      // Verify the document structure is preserved
      expect(backToMarkdown.data).toContain('# Understanding Quantum Computing');
      expect(backToMarkdown.data).toContain('[!info]');
      expect(backToMarkdown.data).toContain('$$');
      expect(backToMarkdown.data).toContain('|\\psi\\rangle');
      expect(backToMarkdown.data).toContain('| Concept | Classical | Quantum |');
      expect(backToMarkdown.data).toContain('[[Quantum Mechanics Basics]]');
      expect(backToMarkdown.data).toContain('[!warning]');
      expect(backToMarkdown.data).toContain('[bookmark](https://quantum-computing.ibm.com)');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle malformed markdown gracefully', async () => {
      const malformedMarkdown = `# Heading
      
> [!invalid-callout
> Missing closing bracket

| Table | Missing |
| --- |
| Incomplete | 

$$
Unclosed math block

[[Unclosed wikilink

$Unclosed inline math`;

      const result = await markdownToLexical(malformedMarkdown);
      expect(result.success).toBe(true); // Should still parse what it can
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
    });

    it('should handle empty and whitespace-only content', async () => {
      const testCases = ['', '   ', '\n\n\n', '\t\t\t'];
      
      for (const testCase of testCases) {
        const result = await markdownToLexical(testCase);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
      }
    });

    it('should handle very long content', async () => {
      const longContent = Array(100).fill(`# Section

This is a paragraph with **bold** and *italic* text.

> [!note]
> This is a callout with [[wikilink]] and $math$.

| Col1 | Col2 |
| --- | --- |
| Data | More data |

$$
x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}
$$

`).join('\n\n');

      const result = await markdownToLexical(longContent);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data.length).toBeGreaterThan(1000);
    });

    it('should handle special characters and unicode', async () => {
      const unicodeMarkdown = `# Unicode Test 🚀

This contains various unicode characters: 
- Emoji: 😀 🎉 ⚡ 🔬
- Math symbols: ∑ ∫ ∞ ≈ ≠
- Foreign text: こんにちは 你好 مرحبا

> [!note] 📝
> Unicode in callouts works too!

| Symbol | Meaning |
| --- | --- |
| ∑ | Summation |
| ∫ | Integral |
| ∞ | Infinity |

Math with unicode: $∑_{i=1}^{∞} \\frac{1}{i^2} = \\frac{π^2}{6}$`;

      const result = await markdownToLexical(unicodeMarkdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('🚀');
      expect(backToMarkdown.data).toContain('こんにちは');
      expect(backToMarkdown.data).toContain('∑');
    });
  });

  describe('Performance and Statistics', () => {
    it('should provide conversion statistics', async () => {
      const markdown = `# Test Document

Content with **formatting** and [[links]].

> [!note]
> Callout content

| Table | Data |
| --- | --- |
| Row | Value |

$E = mc^2$`;

      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      expect(result.metadata).toBeDefined();
      expect(result.metadata?.processingTime).toBeGreaterThan(0);
      expect(result.metadata?.nodeCount).toBeGreaterThan(0);
      
      const stats = getStats();
      expect(stats).toBeDefined();
      expect(stats.conversions).toBeGreaterThan(0);
    });

    it('should handle concurrent conversions', async () => {
      const markdowns = [
        '# Document 1\n\nContent with **bold**.',
        '# Document 2\n\n> [!note]\n> Callout content.',
        '# Document 3\n\n[[Wikilink]] and $math$.',
        '# Document 4\n\n| Table | Data |\n| --- | --- |\n| Row | Value |',
      ];

      const promises = markdowns.map(md => markdownToLexical(md));
      const results = await Promise.all(promises);
      
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Configuration and Customization', () => {
    it('should respect platform-specific settings', async () => {
      // Test with only Ghost features enabled
      await configure({
        options: {
          enableGhostFeatures: true,
          enableObsidianFeatures: false,
        },
      });

      const markdown = `> [!note]
> Ghost callout

[[Obsidian wikilink]]

$Obsidian math$`;

      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      
      // Ghost features should work
      expect(backToMarkdown.data).toContain('[!note]');
      
      // Obsidian features might not be processed as expected
      // (This depends on the specific implementation)
    });

    it('should handle custom performance settings', async () => {
      await configure({
        options: {
          performance: {
            batchSize: 100,
            useStringBuilder: true,
            largeDocumentThreshold: 1000,
            enableConverterCache: true,
          },
        },
      });

      const markdown = '# Test\n\nContent for performance testing.';
      const result = await markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
    });
  });
});
