/**
 * Comprehensive tests for the Hybrid Lexical Parser system
 * Tests official Lexical transformers integration with custom Ghost/Obsidian extensions
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import {
  HybridLexicalParser,
  markdownToLexical,
  lexicalToMarkdown,
  configure,
  reset,
  initializeHybridParser,
} from '../../src/utils/lexical-parser/hybrid';
import type { ParserConfig, ConversionOptions } from '../../src/utils/lexical-parser/hybrid';

describe('Hybrid Lexical Parser', () => {
  beforeEach(async () => {
    // Reset parser state before each test
    reset();
    
    // Initialize with default configuration
    await initializeHybridParser();
  });

  afterEach(() => {
    // Clean up after each test
    reset();
  });

  describe('Initialization and Configuration', () => {
    it('should initialize with default configuration', async () => {
      const parser = HybridLexicalParser.getInstance();
      expect(parser).toBeDefined();
    });

    it('should accept custom configuration', async () => {
      const customConfig: Partial<ParserConfig> = {
        options: {
          enableGhostFeatures: true,
          enableObsidianFeatures: false,
        },
      };

      await initializeHybridParser(customConfig);
      const parser = HybridLexicalParser.getInstance();
      expect(parser).toBeDefined();
    });

    it('should validate configuration', async () => {
      const invalidConfig = {
        options: {
          enableGhostFeatures: 'invalid' as any,
        },
      };

      await expect(initializeHybridParser(invalidConfig)).rejects.toThrow();
    });
  });

  describe('Official Lexical Transformers', () => {
    describe('Basic Text Formatting', () => {
      it('should handle bold text with **', async () => {
        const markdown = '**bold text**';
        const result = await markdownToLexical(markdown);
        
        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toBe('**bold text**');
      });

      it('should handle italic text with *', async () => {
        const markdown = '*italic text*';
        const result = await markdownToLexical(markdown);
        
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toBe('*italic text*');
      });

      it('should handle strikethrough text', async () => {
        const markdown = '~~strikethrough~~';
        const result = await markdownToLexical(markdown);
        
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toBe('~~strikethrough~~');
      });

      it('should handle inline code', async () => {
        const markdown = '`inline code`';
        const result = await markdownToLexical(markdown);
        
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toBe('`inline code`');
      });
    });

    describe('Headings', () => {
      it('should handle all heading levels', async () => {
        const headings = [
          '# Heading 1',
          '## Heading 2', 
          '### Heading 3',
          '#### Heading 4',
          '##### Heading 5',
          '###### Heading 6',
        ];

        for (const heading of headings) {
          const result = await markdownToLexical(heading);
          expect(result.success).toBe(true);
          
          const backToMarkdown = await lexicalToMarkdown(result.data!);
          expect(backToMarkdown.success).toBe(true);
          expect(backToMarkdown.data).toBe(heading);
        }
      });
    });

    describe('Lists', () => {
      it('should handle unordered lists', async () => {
        const markdown = `- Item 1
- Item 2
- Item 3`;
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toContain('- Item 1');
      });

      it('should handle ordered lists', async () => {
        const markdown = `1. First item
2. Second item
3. Third item`;
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toContain('1. First item');
      });

      it('should handle nested lists', async () => {
        const markdown = `- Item 1
  - Nested item 1
  - Nested item 2
- Item 2`;
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toContain('- Item 1');
      });
    });

    describe('Quotes', () => {
      it('should handle simple blockquotes', async () => {
        const markdown = '> This is a quote';
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toBe('> This is a quote');
      });

      it('should handle multi-line blockquotes', async () => {
        const markdown = `> This is a multi-line quote
> that spans multiple lines`;
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toContain('> This is a multi-line quote');
      });
    });

    describe('Code Blocks', () => {
      it('should handle fenced code blocks', async () => {
        const markdown = '```javascript\nconsole.log("Hello, world!");\n```';
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toContain('```');
        expect(backToMarkdown.data).toContain('console.log');
      });

      it('should preserve language specification', async () => {
        const markdown = '```typescript\ninterface Test { name: string; }\n```';
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toContain('typescript');
      });
    });

    describe('Links', () => {
      it('should handle inline links', async () => {
        const markdown = '[OpenAI](https://openai.com)';
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toBe('[OpenAI](https://openai.com)');
      });

      it('should handle links with titles', async () => {
        const markdown = '[OpenAI](https://openai.com "OpenAI Homepage")';
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toContain('OpenAI');
        expect(backToMarkdown.data).toContain('https://openai.com');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid markdown gracefully', async () => {
      const invalidMarkdown = '```\nunclosed code block';
      
      const result = await markdownToLexical(invalidMarkdown);
      expect(result.success).toBe(true); // Should still parse, just not as expected
    });

    it('should handle empty input', async () => {
      const result = await markdownToLexical('');
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle null/undefined input', async () => {
      const result = await markdownToLexical(null as any);
      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should handle large documents efficiently', async () => {
      const largeMarkdown = Array(1000).fill('# Heading\n\nParagraph with **bold** and *italic* text.\n\n').join('');
      
      const startTime = Date.now();
      const result = await markdownToLexical(largeMarkdown);
      const endTime = Date.now();
      
      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should provide performance metadata', async () => {
      const markdown = '# Test\n\nSome content';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      expect(result.metadata).toBeDefined();
      expect(result.metadata?.processingTime).toBeGreaterThan(0);
    });
  });
});
