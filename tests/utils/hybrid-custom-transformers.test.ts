/**
 * Tests for custom Ghost and Obsidian transformers in the Hybrid Lexical Parser
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import {
  markdownToLexical,
  lexicalToMarkdown,
  initializeHybridParser,
  reset,
} from '../../src/utils/lexical-parser/hybrid';

describe('Custom Transformers', () => {
  beforeEach(async () => {
    reset();
    await initializeHybridParser({
      options: {
        enableGhostFeatures: true,
        enableObsidianFeatures: true,
      },
    });
  });

  afterEach(() => {
    reset();
  });

  describe('Ghost Callouts', () => {
    it('should handle note callouts', async () => {
      const markdown = '> [!note]\n> This is a note callout';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('[!note]');
    });

    it('should handle warning callouts', async () => {
      const markdown = '> [!warning]\n> This is a warning';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('[!warning]');
    });

    it('should handle all supported callout types', async () => {
      const calloutTypes = ['note', 'info', 'warning', 'danger', 'success', 'tip', 'important', 'caution'];
      
      for (const type of calloutTypes) {
        const markdown = `> [!${type}]\n> This is a ${type} callout`;
        
        const result = await markdownToLexical(markdown);
        expect(result.success).toBe(true);
        
        const backToMarkdown = await lexicalToMarkdown(result.data!);
        expect(backToMarkdown.success).toBe(true);
        expect(backToMarkdown.data).toContain(`[!${type}]`);
      }
    });

    it('should handle callouts with content after marker', async () => {
      const markdown = '> [!note] Important Note\n> This is the content';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('[!note]');
      expect(backToMarkdown.data).toContain('Important Note');
    });

    it('should fall back to regular quote for unknown callout types', async () => {
      const markdown = '> [!unknown]\n> This should be a regular quote';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      // Should be treated as regular quote, not callout
      expect(backToMarkdown.data).toContain('>');
    });
  });

  describe('Ghost Bookmarks', () => {
    it('should handle simple bookmark syntax', async () => {
      const markdown = '[bookmark](https://example.com)';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('[bookmark](https://example.com)');
    });

    it('should handle bookmarks with metadata', async () => {
      const markdown = `[bookmark](https://example.com)
title: Example Website
description: A great example website
image: https://example.com/image.jpg`;
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('[bookmark](https://example.com)');
      expect(backToMarkdown.data).toContain('title: Example Website');
    });

    it('should handle bookmarks with all metadata fields', async () => {
      const markdown = `[bookmark](https://example.com)
title: Example Website
description: A comprehensive example
image: https://example.com/image.jpg
author: John Doe
publisher: Example Corp`;
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('author: John Doe');
      expect(backToMarkdown.data).toContain('publisher: Example Corp');
    });
  });

  describe('Obsidian Wikilinks', () => {
    it('should handle simple wikilinks', async () => {
      const markdown = '[[Page Name]]';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toBe('[[Page Name]]');
    });

    it('should handle wikilinks with aliases', async () => {
      const markdown = '[[Page Name|Display Text]]';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toBe('[[Page Name|Display Text]]');
    });

    it('should handle wikilinks with headings', async () => {
      const markdown = '[[Page Name#Section]]';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toBe('[[Page Name#Section]]');
    });

    it('should handle wikilinks with headings and aliases', async () => {
      const markdown = '[[Page Name#Section|Custom Text]]';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toBe('[[Page Name#Section|Custom Text]]');
    });

    it('should clean file extensions from targets', async () => {
      const markdown = '[[Page Name.md]]';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toBe('[[Page Name]]'); // .md should be removed
    });
  });

  describe('Enhanced Tables', () => {
    it('should handle simple tables', async () => {
      const markdown = `| Header 1 | Header 2 |
| --- | --- |
| Cell 1 | Cell 2 |
| Cell 3 | Cell 4 |`;
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('| Header 1 | Header 2 |');
      expect(backToMarkdown.data).toContain('| Cell 1 | Cell 2 |');
    });

    it('should handle tables with alignments', async () => {
      const markdown = `| Left | Center | Right |
| :--- | :---: | ---: |
| L1 | C1 | R1 |
| L2 | C2 | R2 |`;
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain(':---');
      expect(backToMarkdown.data).toContain(':---:');
      expect(backToMarkdown.data).toContain('---:');
    });

    it('should handle tables without headers', async () => {
      const markdown = `| Cell 1 | Cell 2 |
| Cell 3 | Cell 4 |`;
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('| Cell 1 | Cell 2 |');
    });

    it('should escape pipe characters in cell content', async () => {
      const markdown = `| Header |
| --- |
| Content with \\| pipe |`;
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('\\|');
    });
  });

  describe('Math Expressions', () => {
    it('should handle inline math', async () => {
      const markdown = 'This is inline math: $E = mc^2$ in the text.';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('$E = mc^2$');
    });

    it('should handle block math', async () => {
      const markdown = `$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$`;
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('$$');
      expect(backToMarkdown.data).toContain('\\int');
    });

    it('should handle display math with \\[...\\] syntax', async () => {
      const markdown = `\\[
f(x) = \\sum_{n=0}^{\\infty} \\frac{f^{(n)}(a)}{n!}(x-a)^n
\\]`;
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      // Should convert to $$ format
      expect(backToMarkdown.data).toContain('$$');
      expect(backToMarkdown.data).toContain('\\sum');
    });

    it('should not parse incomplete math expressions', async () => {
      const markdown = 'This has incomplete math: $E = mc^2 without closing';
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      // Should not be parsed as math
      expect(backToMarkdown.data).not.toMatch(/\$E = mc\^2\$/);
    });

    it('should handle complex math expressions', async () => {
      const markdown = `$$
\\begin{align}
\\nabla \\times \\vec{E} &= -\\frac{\\partial \\vec{B}}{\\partial t} \\\\
\\nabla \\times \\vec{B} &= \\mu_0\\vec{J} + \\mu_0\\epsilon_0\\frac{\\partial \\vec{E}}{\\partial t}
\\end{align}
$$`;
      
      const result = await markdownToLexical(markdown);
      expect(result.success).toBe(true);
      
      const backToMarkdown = await lexicalToMarkdown(result.data!);
      expect(backToMarkdown.success).toBe(true);
      expect(backToMarkdown.data).toContain('\\begin{align}');
      expect(backToMarkdown.data).toContain('\\nabla');
    });
  });
});
