/**
 * Ported edge case tests from the original lexical-parser
 * These tests ensure robust error handling and edge case support in the new Markdown class
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Markdown } from '../../src/markdown';

describe('Ported Edge Case Tests', () => {
  let parser: Markdown;

  beforeEach(() => {
    parser = new Markdown();
  });

  afterEach(() => {
    parser.destroy();
  });

  describe('Malformed Markdown Content', () => {
    it('should handle unclosed code blocks', async () => {
      const malformedMarkdown = '```javascript\nconsole.log("unclosed");';
      const result = await parser.markdownToLexical(malformedMarkdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle unclosed formatting', async () => {
      const malformedMarkdown = '**unclosed bold text';
      const result = await parser.markdownToLexical(malformedMarkdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle malformed links', async () => {
      const malformedLinks = [
        '[Unclosed link text',
        '[Link](unclosed url',
        '[]()',
        '[]',
        '[Link with spaces]( invalid url )',
        '[](data:invalid)'
      ];

      for (const markdown of malformedLinks) {
        const result = await parser.markdownToLexical(markdown);
        expect(result.success).toBe(true);
        expect(result.data?.root.children.length).toBeGreaterThan(0);
      }
    });

    it('should handle mixed line endings', async () => {
      const mixedLineEndings = 'Line 1\r\nLine 2\rLine 3\nLine 4';
      const result = await parser.markdownToLexical(mixedLineEndings);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle excessive whitespace', async () => {
      const excessiveWhitespace = 'Para 1\n\n\n\n\n\n\n\nPara 2\t\t\t\t\nPara 3';
      const result = await parser.markdownToLexical(excessiveWhitespace);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle null bytes and control characters', async () => {
      const problematicContent = 'Text with\0null\x01byte\x02and\x03control\x04chars';
      const result = await parser.markdownToLexical(problematicContent);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });
  });

  describe('Unicode and Special Characters', () => {
    it('should handle various unicode characters', async () => {
      const unicodeContent = `# Unicode Test 🚀

Emojis: 😀 😃 😄 😁 😆 😅 😂 🤣

Languages:
- English: Hello World
- Chinese: 你好世界
- Japanese: こんにちは世界
- Arabic: مرحبا بالعالم
- Russian: Привет мир
- Hindi: नमस्ते दुनिया

Math symbols: ∑ ∫ ∞ ≠ ≤ ≥ ± × ÷

Special quotes: "smart quotes" 'apostrophes'
Dashes: – — ‒ ―`;

      const result = await parser.markdownToLexical(unicodeContent);
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle zero-width characters', async () => {
      const zeroWidthContent = 'Text\u200Bwith\u200Czero\u200Dwidth\uFEFFcharacters';
      const result = await parser.markdownToLexical(zeroWidthContent);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle combining characters', async () => {
      const combiningContent = 'e\u0301 a\u0300 o\u0302 n\u0303'; // é à ô ñ
      const result = await parser.markdownToLexical(combiningContent);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle right-to-left text', async () => {
      const rtlContent = 'English text مع النص العربي and back to English';
      const result = await parser.markdownToLexical(rtlContent);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });
  });

  describe('Content Boundary Cases', () => {
    it('should handle empty and whitespace-only content', async () => {
      const testCases = ['', '   ', '\n\n\n', '\t\t\t', ' \n \t \n '];

      for (const content of testCases) {
        const result = await parser.markdownToLexical(content);
        expect(result.success).toBe(true);
        expect(result.data?.root.children).toHaveLength(0);
      }
    });

    it('should handle content with only special characters', async () => {
      const specialOnly = '!@#$%^&*()_+-={}[]|\\:";\'<>?,./~`';
      const result = await parser.markdownToLexical(specialOnly);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle mixed valid and invalid markdown', async () => {
      const mixedContent = `# Valid Heading

**Valid bold** text with invalid **unclosed bold

- Valid list item
- Another valid item
- Invalid item with **unclosed formatting

\`\`\`
Valid code block
\`\`\`

\`\`\`unclosed
Invalid code block`;

      const result = await parser.markdownToLexical(mixedContent);
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle very long content', async () => {
      const longContent = 'A'.repeat(10000);
      const result = await parser.markdownToLexical(longContent);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle deeply nested structures', async () => {
      let nestedList = '';
      for (let i = 0; i < 20; i++) {
        nestedList += '  '.repeat(i) + '- Item at level ' + i + '\n';
      }

      const result = await parser.markdownToLexical(nestedList);
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });
  });

  describe('Malformed Lexical Content', () => {
    it('should handle invalid document structure', async () => {
      const malformedDoc = {
        root: {
          type: 'root' as const,
          children: [
            null as any,
            { type: null } as any,
            { /* missing type */ } as any,
            {
              type: 'paragraph',
              children: [
                { type: 'text', text: 'Valid text', detail: 0, format: 0, mode: 'normal' as const, style: '', version: 1 }
              ],
              direction: 'ltr' as const,
              format: '',
              indent: 0,
              version: 1
            }
          ],
          direction: 'ltr' as const,
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = await parser.lexicalToMarkdown(malformedDoc);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Document validation failed');
    });

    it('should handle nodes with missing required properties', async () => {
      const malformedDoc = {
        root: {
          type: 'root' as const,
          children: [
            {
              type: 'text',
              // Missing required text property
            } as any,
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: 'Valid text',
                  detail: 0,
                  format: 0,
                  mode: 'normal' as const,
                  style: '',
                  version: 1
                }
              ],
              direction: 'ltr' as const,
              format: '',
              indent: 0,
              version: 1
            }
          ],
          direction: 'ltr' as const,
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = await parser.lexicalToMarkdown(malformedDoc);
      // Should handle gracefully, either with warnings or fallback
      expect(result.success).toBe(true);
    });

    it('should handle nodes with invalid formatting flags', async () => {
      const invalidFormattingDoc = {
        root: {
          type: 'root' as const,
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Text with invalid format',
              detail: 0,
              format: 999999, // Invalid format value
              mode: 'normal' as const,
              style: '',
              version: 1
            }],
            direction: 'ltr' as const,
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr' as const,
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = await parser.lexicalToMarkdown(invalidFormattingDoc);
      expect(result.success).toBe(true);
      expect(result.data).toContain('Text with invalid format');
    });

    it('should handle circular references gracefully', async () => {
      const circularDoc: any = {
        root: {
          type: 'root',
          children: [],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      // Create circular reference
      circularDoc.root.children.push(circularDoc.root);

      const result = await parser.lexicalToMarkdown(circularDoc);
      // Should handle gracefully without infinite loops
      expect(result.success).toBe(false);
    });
  });

  describe('Performance and Memory', () => {
    it('should handle multiple rapid conversions', async () => {
      const markdown = '# Test\n\nThis is **bold** text.';

      // Perform multiple conversions rapidly
      const promises = Array.from({ length: 100 }, () =>
        parser.markdownToLexical(markdown)
      );

      const results = await Promise.all(promises);

      // All should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });

    it('should handle large documents efficiently', async () => {
      const largeMarkdown = Array.from({ length: 1000 }, (_, i) =>
        `## Section ${i}\n\nThis is paragraph ${i} with **bold** and *italic* text.\n\n- Item 1\n- Item 2\n- Item 3\n`
      ).join('\n');

      const startTime = Date.now();
      const result = await parser.markdownToLexical(largeMarkdown);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);

      // Should complete in reasonable time (less than 5 seconds)
      expect(endTime - startTime).toBeLessThan(5000);
    });
  });
});
