/**
 * Ported round-trip tests from the original lexical-parser
 * These tests ensure content preservation through markdown ↔ lexical conversions
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Markdown, roundTrip } from '../../src/markdown';

describe('Ported Round-trip Tests', () => {
  let parser: Markdown;

  beforeEach(() => {
    parser = new Markdown();
  });

  afterEach(() => {
    parser.destroy();
  });

  describe('Basic Content Preservation', () => {
    it('should preserve headings through round-trip', async () => {
      const testCases = [
        '# Heading 1',
        '## Heading 2',
        '### Heading 3',
        '#### Heading 4',
        '##### Heading 5',
        '###### Heading 6'
      ];

      for (const originalMarkdown of testCases) {
        const lexicalResult = await parser.markdownToLexical(originalMarkdown);
        expect(lexicalResult.success).toBe(true);

        const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
        expect(markdownResult.success).toBe(true);
        expect(markdownResult.data).toBe(originalMarkdown);
      }
    });

    it('should preserve text formatting through round-trip', async () => {
      const testCases = [
        { input: '**bold text**', expected: '**bold text**' },
        { input: '*italic text*', expected: '*italic text*' }, // Star italic stays star italic
        { input: '_italic text_', expected: '*italic text*' }, // Underscore italic converts to star italic
        { input: '`inline code`', expected: '`inline code`' },
        { input: '~~strikethrough~~', expected: '~~strikethrough~~' },
      ];

      for (const testCase of testCases) {
        const lexicalResult = await parser.markdownToLexical(testCase.input);
        expect(lexicalResult.success).toBe(true);

        const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
        expect(markdownResult.success).toBe(true);
        expect(markdownResult.data).toBe(testCase.expected);
      }
    });

    it('should preserve simple paragraphs through round-trip', async () => {
      const originalMarkdown = 'This is a simple paragraph with some text.';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });

    it('should preserve empty content through round-trip', async () => {
      const originalMarkdown = '';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe('');
    });
  });

  describe('List Preservation', () => {
    it('should preserve unordered lists through round-trip', async () => {
      const originalMarkdown = `- Item 1
- Item 2
- Item 3`;

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });

    it('should preserve ordered lists through round-trip', async () => {
      const originalMarkdown = `1. First item
2. Second item
3. Third item`;

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });

    it('should preserve nested lists through round-trip', async () => {
      const originalMarkdown = `- Item 1
  - Nested item 1
  - Nested item 2
- Item 2`;

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      // Content should be preserved (exact formatting may vary)
      expect(markdownResult.data).toContain('Item 1');
      expect(markdownResult.data).toContain('Nested item 1');
      expect(markdownResult.data).toContain('Nested item 2');
      expect(markdownResult.data).toContain('Item 2');
    });
  });

  describe('Code Block Preservation', () => {
    it('should preserve fenced code blocks through round-trip', async () => {
      const originalMarkdown = '```javascript\nconsole.log("Hello, world!");\n```';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toContain('```javascript');
      expect(markdownResult.data).toContain('console.log("Hello, world!");');
      expect(markdownResult.data).toContain('```');
    });

    it('should preserve code blocks without language through round-trip', async () => {
      const originalMarkdown = '```\nsome code\n```';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toContain('```');
      expect(markdownResult.data).toContain('some code');
    });
  });

  describe('Link Preservation', () => {
    it('should preserve inline links through round-trip', async () => {
      const originalMarkdown = 'This is a [link](https://example.com) in text.';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toContain('[link](https://example.com)');
    });

    it('should preserve links with titles through round-trip', async () => {
      const originalMarkdown = '[Link](https://example.com "Example Title")';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toContain('[Link](https://example.com "Example Title")');
    });
  });

  describe('Blockquote Preservation', () => {
    it('should preserve simple blockquotes through round-trip', async () => {
      const originalMarkdown = '> This is a blockquote';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toContain('> This is a blockquote');
    });

    it('should preserve multi-line blockquotes through round-trip', async () => {
      const originalMarkdown = '> Line 1\n> Line 2\n> Line 3';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toContain('> Line 1');
      expect(markdownResult.data).toContain('> Line 2');
      expect(markdownResult.data).toContain('> Line 3');
    });
  });

  describe('Complex Content Preservation', () => {
    it('should preserve mixed content through round-trip', async () => {
      const originalMarkdown = `# Title

This is **bold** and *italic* text.

- List item 1
- List item 2

\`\`\`javascript
console.log("code");
\`\`\`

> Blockquote text

[Link](https://example.com)`;

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      // Check that all major elements are preserved
      expect(markdownResult.data).toContain('# Title');
      expect(markdownResult.data).toContain('**bold**');
      expect(markdownResult.data).toContain('italic');
      expect(markdownResult.data).toContain('- List item 1');
      expect(markdownResult.data).toContain('- List item 2');
      expect(markdownResult.data).toContain('```javascript');
      expect(markdownResult.data).toContain('console.log("code");');
      expect(markdownResult.data).toContain('> Blockquote text');
      expect(markdownResult.data).toContain('[Link](https://example.com)');
    });

    it('should preserve formatting within structures through round-trip', async () => {
      const originalMarkdown = `# **Bold** Heading

- **Bold** list item
- *Italic* list item
- \`Code\` list item

> **Bold** in blockquote`;

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      // Check that formatting is preserved within structures
      expect(markdownResult.data).toContain('**Bold**');
      expect(markdownResult.data).toContain('*Italic*');
      expect(markdownResult.data).toContain('`Code`');
    });
  });

  describe('Advanced Features Preservation', () => {
    it('should preserve Ghost callouts through round-trip', async () => {
      const originalMarkdown = '> [!note]\n> This is a note callout';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      expect(markdownResult.data).toContain('[!note]');
      expect(markdownResult.data).toContain('This is a note callout');
    });

    it('should preserve Obsidian wikilinks through round-trip', async () => {
      const originalMarkdown = '[[Target Page|Display Text]]';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      // The wikilink should be preserved in some form (may be converted to regular link)
      expect(markdownResult.data).toContain('Display Text');
      expect(markdownResult.data).toContain('Target Page');
    });

    it('should preserve Obsidian tags through round-trip', async () => {
      const originalMarkdown = 'This has a #tag in it';

      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      expect(markdownResult.data).toContain('#tag');
    });
  });

  describe('Convenience Function Tests', () => {
    it('should work with the roundTrip convenience function', async () => {
      const originalMarkdown = 'Hello **world** with *formatting*';
      const result = await roundTrip(originalMarkdown);

      expect(result.success).toBe(true);
      expect(result.data).toContain('Hello');
      expect(result.data).toContain('**world**');
      expect(result.data).toContain('formatting');
    });

    it('should handle complex content with roundTrip function', async () => {
      const originalMarkdown = `# Title

This is a test with:
- **Bold** text
- *Italic* text
- \`Code\` text

> A blockquote

[A link](https://example.com)`;

      const result = await roundTrip(originalMarkdown);

      expect(result.success).toBe(true);
      expect(result.data).toContain('# Title');
      expect(result.data).toContain('**Bold**');
      expect(result.data).toContain('*Italic*');
      expect(result.data).toContain('`Code`');
      expect(result.data).toContain('> A blockquote');
      expect(result.data).toContain('[A link](https://example.com)');
    });
  });
});
