/**
 * Debug script to test the hybrid parser
 */

async function debug() {
  try {
    console.log('Testing import...');
    const hybridModule = await import('./src/utils/lexical-parser/hybrid/index.ts');
    console.log('Import successful');
    console.log('Available exports:', Object.keys(hybridModule));

    const { markdownToLexical, initializeHybridParser } = hybridModule;
    console.log('markdownToLexical:', typeof markdownToLexical);
    console.log('initializeHybridParser:', typeof initializeHybridParser);

    if (typeof initializeHybridParser === 'function') {
      console.log('Initializing hybrid parser...');
      await initializeHybridParser();
      console.log('Parser initialized successfully');

      console.log('Testing simple markdown conversion...');
      const result = await markdownToLexical('**bold text**');

      console.log('Result:', JSON.stringify(result, null, 2));

      if (!result.success) {
        console.log('Conversion failed!');
        console.log('Errors:', result.errors);
      } else {
        console.log('Conversion succeeded!');
        console.log('Data:', result.data);
      }
    } else {
      console.log('initializeHybridParser is not available');
    }
  } catch (error) {
    console.error('Error during debug:', error);
    console.error('Stack:', error.stack);
  }
}

debug();
