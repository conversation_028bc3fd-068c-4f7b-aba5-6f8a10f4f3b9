{"name": "@lexical/file", "description": "This package provides the file import/export feature for Lexical.", "keywords": ["lexical", "editor", "rich-text", "file", "import", "export"], "license": "MIT", "version": "0.34.0", "main": "LexicalFile.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-file"}, "module": "LexicalFile.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalFile.dev.mjs", "production": "./LexicalFile.prod.mjs", "node": "./LexicalFile.node.mjs", "default": "./LexicalFile.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalFile.dev.js", "production": "./LexicalFile.prod.js", "default": "./LexicalFile.js"}}}, "dependencies": {"lexical": "0.34.0"}}