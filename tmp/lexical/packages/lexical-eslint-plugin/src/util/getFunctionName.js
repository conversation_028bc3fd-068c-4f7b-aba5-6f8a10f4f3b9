/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
// @ts-check

const {getParentAssignmentName} = require('./getParentAssignmentName');

/**
 * Gets the static name of a function AST node. For function declarations it is
 * easy. For anonymous function expressions it is much harder. If you search for
 * `IsAnonymousFunctionDefinition()` in the ECMAScript spec you'll find places
 * where J<PERSON> gives anonymous function expressions names. We roughly detect the
 * same AST nodes with some exceptions to better fit our use case.
 *
 * @param {import('eslint').Rule.Node} node
 */
module.exports.getFunctionName = function getFunctionName(node) {
  if (
    node.type === 'FunctionDeclaration' ||
    (node.type === 'FunctionExpression' && node.id)
  ) {
    // function $function() {}
    // const whatever = function $function() {};
    //
    // Function declaration or function expression names win over any
    // assignment statements or other renames.
    return node.id;
  } else if (
    node.type === 'FunctionExpression' ||
    node.type === 'ArrowFunctionExpression'
  ) {
    // This checks for assignments such as
    // const $function = function () {};
    // const $function = () => {};
    return getParentAssignmentName(node);
  } else {
    return undefined;
  }
};
