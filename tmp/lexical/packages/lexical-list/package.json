{"name": "@lexical/list", "description": "This package provides the list feature for Lexical.", "keywords": ["lexical", "editor", "rich-text", "list"], "license": "MIT", "version": "0.34.0", "main": "LexicalList.js", "types": "index.d.ts", "dependencies": {"@lexical/selection": "0.34.0", "@lexical/utils": "0.34.0", "lexical": "0.34.0"}, "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-list"}, "module": "LexicalList.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalList.dev.mjs", "production": "./LexicalList.prod.mjs", "node": "./LexicalList.node.mjs", "default": "./LexicalList.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalList.dev.js", "production": "./LexicalList.prod.js", "default": "./LexicalList.js"}}}}