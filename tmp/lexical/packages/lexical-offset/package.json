{"name": "@lexical/offset", "description": "This package contains selection offset helpers for Lexical.", "keywords": ["lexical", "editor", "rich-text", "offset"], "license": "MIT", "version": "0.34.0", "main": "LexicalOffset.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-offset"}, "module": "LexicalOffset.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalOffset.dev.mjs", "production": "./LexicalOffset.prod.mjs", "node": "./LexicalOffset.node.mjs", "default": "./LexicalOffset.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalOffset.dev.js", "production": "./LexicalOffset.prod.js", "default": "./LexicalOffset.js"}}}, "dependencies": {"lexical": "0.34.0"}}