<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Lexical Playground" />
    <title>Lexical Playground - Split Screen</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <script>
      (function () {
        const {port, hostname, protocol} = window.location;
        const leftIframe = document.createElement('iframe');
        leftIframe.src = `${protocol}//${hostname}:${port}${window.location.search}`;
        leftIframe.style.cssText =
          'border: 0; width: calc(50% - 1px); position: fixed; top: 0; left: 0; height: 100%;';
        leftIframe.setAttribute('name', 'left');
        document.body.appendChild(leftIframe);
        const rightIframe = document.createElement('iframe');
        rightIframe.src = `${protocol}//${hostname}:${port}${window.location.search}`;
        rightIframe.style.cssText =
          'border: 0; width: calc(50% - 1px); position: fixed; top: 0; left: calc(50% + 1px); height: 100%;';
        rightIframe.setAttribute('name', 'right');
        document.body.appendChild(rightIframe);
      })();
    </script>
  </body>
</html>
