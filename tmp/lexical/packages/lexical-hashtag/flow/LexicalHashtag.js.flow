/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */

import type {
  EditorConfig,
  LexicalNode,
  NodeKey,
  SerializedTextNode,
} from 'lexical';

import {TextNode} from 'lexical';

declare export class HashtagNode extends TextNode {
  static getType(): string;
  // $FlowFixMe
  static clone(node: HashtagNode): HashtagNode;
  static importJSON(serializedNode: SerializedTextNode): HashtagNode;
  constructor(text: string, key?: NodeKey): void;
  createDOM(config: EditorConfig): HTMLElement;
  canInsertTextBefore(): boolean;
  isTextEntity(): true;
  exportJSON(): SerializedTextNode;
}
declare export function $createHashtagNode(text?: string): HashtagNode;
declare export function $isHashtagNode(
  node: ?LexicalNode,
): node is HashtagNode;
