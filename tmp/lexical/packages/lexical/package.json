{"name": "lexical", "description": "Lexical is an extensible text editor framework that provides excellent reliability, accessible and performance.", "keywords": ["react", "lexical", "editor", "contenteditable", "rich-text"], "license": "MIT", "version": "0.34.0", "main": "Lexical.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical"}, "module": "Lexical.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./Lexical.dev.mjs", "production": "./Lexical.prod.mjs", "node": "./Lexical.node.mjs", "default": "./Lexical.mjs"}, "require": {"types": "./index.d.ts", "development": "./Lexical.dev.js", "production": "./Lexical.prod.js", "default": "./Lexical.js"}}}}