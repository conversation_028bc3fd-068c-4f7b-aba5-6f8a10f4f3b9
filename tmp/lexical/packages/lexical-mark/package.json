{"name": "@lexical/mark", "description": "This package contains helpers and nodes for wrapping content in marks for Lexical.", "keywords": ["lexical", "editor", "rich-text", "mark"], "license": "MIT", "version": "0.34.0", "main": "LexicalMark.js", "types": "index.d.ts", "dependencies": {"@lexical/utils": "0.34.0", "lexical": "0.34.0"}, "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-mark"}, "module": "LexicalMark.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalMark.dev.mjs", "production": "./LexicalMark.prod.mjs", "node": "./LexicalMark.node.mjs", "default": "./LexicalMark.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalMark.dev.js", "production": "./LexicalMark.prod.js", "default": "./LexicalMark.js"}}}}