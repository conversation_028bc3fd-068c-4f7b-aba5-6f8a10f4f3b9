{"name": "@lexical/html", "description": "This package contains HTML helpers and functionality for Lexical.", "keywords": ["lexical", "editor", "rich-text", "html"], "license": "MIT", "version": "0.34.0", "main": "LexicalHtml.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/facebook/lexical", "directory": "packages/lexical-html"}, "dependencies": {"@lexical/selection": "0.34.0", "@lexical/utils": "0.34.0", "lexical": "0.34.0"}, "module": "LexicalHtml.mjs", "sideEffects": false, "exports": {".": {"import": {"types": "./index.d.ts", "development": "./LexicalHtml.dev.mjs", "production": "./LexicalHtml.prod.mjs", "node": "./LexicalHtml.node.mjs", "default": "./LexicalHtml.mjs"}, "require": {"types": "./index.d.ts", "development": "./LexicalHtml.dev.js", "production": "./LexicalHtml.prod.js", "default": "./LexicalHtml.js"}}}}