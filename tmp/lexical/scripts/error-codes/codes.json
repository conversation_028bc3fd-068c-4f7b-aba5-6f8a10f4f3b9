{"0": "getLatest() on clone node", "1": "TODO", "2": "Node %s and selection point do not match.", "3": "createOffsetModel: could not find node by key", "4": "Expected node %s to have closest block element node.", "5": "createNodeFromParse: type \"%s\" + not found", "6": "Incorrect node type received in importJSON for %s", "7": "LexicalAutoLinkPlugin: AutoLinkNode, TableCellNode or TableRowNode not registered on editor", "8": "LexicalComposerContext.useLexicalComposerContext: cannot find a LexicalComposerContext", "9": "Unexpected parent context null on a nested composer", "10": "TablePlugin: TableNode, TableCellNode or TableRowNode not registered on editor", "11": "Expected grid selection", "12": "Expected table cell", "13": "Cannot use method in read-only mode.", "14": "One or more transforms are endlessly triggering additional transforms. May have encountered infinite recursion caused by transforms that have their preconditions too lose and/or conflict with each other.", "15": "Unable to find an active editor state. State helpers or node methods can only be used synchronously during the callback of editor.update() or editorState.read().", "16": "Unable to find an active editor. This method can only be used synchronously during the callback of editor.update().", "17": "parseEditorState: type \"%s\" + not found", "18": "LexicalNode: Node %s does not implement .importJSON().", "19": "updateEditor: selection has been lost because the previously selected nodes have been removed and selection wasn't moved to another node. Ensure selection changes after removing/replacing a selected node.", "20": "Point.getNode: node not found", "21": "getNodes: expected to find AnchorNode", "22": "getNodes: expected to find FocusNode", "23": "getNodes: expected to find GridNode", "24": "getNodes: expected to find GridRowNode", "25": "getNodes: expected to find GridCellNode", "26": "insertText: first node is not a text node", "27": "insertNode: topLevelElement is root node", "28": "insertNodes: cannot insert a non-element into a root node", "29": "insertNodes: cloned parent clone is not an element", "30": "registeredNode: Type %s not found", "31": "<PERSON><PERSON> is not a child of its parent", "32": "$setSelection called on frozen selection object. Ensure selection is cloned before passing in.", "33": "Type %s not in registeredNodes", "34": "Reconciliation: could not find DOM element for node key \"${key}\"", "35": "Listener for type \"command\" requires a \"priority\".", "36": "registerCommand: Command %s not found in command map", "37": "Node %s has not been registered. Ensure node has been passed to createEditor.", "38": "setEditorState: the editor state is empty. Ensure the editor state's root node never becomes empty.", "39": "insertAfter: list node is not parent of list item node", "40": "A ListItemNode must have a ListNode for a parent.", "41": "Expected Grid Cell in Grid Selection", "42": "$mergeGridNodesStrategy: Expected Grid insertion.", "43": "$mergeGridNodesStrategy: Expected selection to be inside of a Grid.", "44": "$getListItemValue: list node is not parent of list item node", "45": "Expected node %s to have a first child.", "46": "append: attempting to append self", "47": "decorate: base method not extended", "48": "updateDOM: prevInnerDOM is null or undefined", "49": "updateDOM: innerDOM is null or undefined", "50": "mergeWithSibling: sibling must be a previous or next sibling", "51": "getTopLevelElementOrThrow: root nodes are not top level elements", "52": "remove: cannot be called on root nodes", "53": "replace: cannot be called on root nodes", "54": "insertBefore: cannot be called on root nodes", "55": "insertAfter: cannot be called on root nodes", "56": "rootNode.append: Only element or decorator nodes can be appended to the root node", "57": "useCharacterLimit: OverflowNode not registered on editor", "58": "LexicalNode: Node %s does not implement .exportJSON().", "59": "LexicalNode: Node %s is an element but .exportJSON() does not have a children array.", "60": "createNode: node does not exist in nodeMap", "61": "reconcileNode: prevNode or nextNode does not exist in nodeMap", "62": "reconcileNode: parentDOM is null", "63": "Expected node with key %s to exist but it's not in the nodeMap.", "64": "LexicalNode: Node %s does not implement .getType().", "65": "LexicalNode: Node %s does not implement .clone().", "66": "Expected node %s to have a parent.", "67": "Expected node %s to have a top parent element.", "68": "getNodesBetween: ancestor is null", "69": "Lexical node does not exist in active editor state. Avoid using the same node references between nested closures from editor.read/editor.update.", "70": "createDOM: base method not extended", "71": "updateDOM: base method not extended", "72": "exportJSON: base method not extended", "73": "Create node: Attempted to create node %s that was not previously registered on the editor. You can use register your custom nodes.", "74": "Create node: Type %s in node %s does not match registered node %s with the same type", "75": "Reconciliation: could not find DOM element for node key %s", "76": "append: attempting to append self", "77": "LexicalAutoLinkPlugin: AutoLinkNode not registered on editor", "78": "window object not found", "79": "MarkdownShortcuts: missing dependency for transformer. Ensure node dependency is included in editor initial config.", "80": "Create node: Attempted to create node %s that was not configured to be used on the editor.", "81": "createBinding: doc is null or undefined", "82": "Expected text, element, or decorator event", "83": "syncPropertiesFromYjs: could not find decorator node", "84": "syncPropertiesAndTextFromYjs: could not find decorator node", "85": "could not find node by key", "86": "Expected text, element, decorator, or linebreak node", "87": "Expected shared type to include type attribute", "88": "Node %s is not registered", "89": "Expected parent to be a collab element node", "90": "getOffset: could not find collab element node", "91": "syncPropertiesFromYjs: could not find element node", "92": "syncChildrenFromYjs: could not find element node", "93": "syncChildrenFromYjs: expected text, element, decorator, or linebreak collab node", "94": "splice: could not find collab element node", "95": "splice: expected offset to be greater than zero", "96": "Expected node %s to have a last child.", "97": "$initializeNode failed. Ensure node has been registered to the editor. You can do this by passing the node class via the \"nodes\" array in the editor config.", "98": "$initializeNode failed. Ensure replacement node is a subclass of the original node.", "99": "Only element or decorator nodes can be inserted in to the root node", "100": "splice: sibling not found", "101": "createChildrenArray: node does not exist in nodeMap", "102": "Can not call $splitNode() on root element", "103": "Expected GridSelection anchor to be (or a child of) GridCellNode", "104": "Expected GridSelection focus to be (or a child of) GridCellNode", "105": "Expected anchorCell to have a parent GridRowNode", "106": "Expected tableNode to have a parent GridNode", "107": "Expected GridCellNode parent to be a GridRowNode", "108": "Expected GridNode children to be GridRowNode", "109": "Expected GridRowNode children to be GridCellNode", "110": "Anchor not found in Grid", "111": "Focus not found in Grid", "112": "Stack.length > 0; can't be undefined", "113": "Lexical node does not exist in active editor state. Avoid using the same node references between nested closures from editorState.read/editor.update.", "114": "Expected to find a parent GridCellNode", "115": "Expected GridCellNode to have a parent GridRowNode", "116": "Expected GridRowNode to have a parent GridNode", "117": "Invalid indent value.", "118": "Expected a RangeSelection or GridSelection", "119": "focusEndRow is not a GridRowNode", "120": "Expected firstTable child to be a row", "121": "Expected row nextSibling to be a row", "122": "Expected nextRowNode not to be null", "123": "Expected GridNode childAtIndex(%s) to be RowNode", "124": "Unexpected empty cell", "125": "Expected row next sibling to be a row", "126": "TabNode does not support setTextContent", "127": "TabNode does not support setDetail", "128": "TabNode does not support setMode", "129": "Expected parentElement of Text not to be null", "130": "LexicalNode: Node %s does not match the serialized type. Check if .exportJSON() is implemented and it is returning the correct type.", "131": "Expected to find LexicalNode from Table Cell DOMNode", "132": "Expected TextNode createDOM to always return a HTMLElement", "133": "Children of a root must be ElementNode", "134": "Expected RangeSelection after insertParagraph", "135": "Expected 'firstBlock' to be an ElementNode", "136": "Expected ancestor to be an ElementNode", "137": "LexicalNode: Node %s does not implement .isInline().", "138": "Children of root nodes must be elements", "139": "includeChildren should only be true for ElementNodes", "140": "insertRangeAfter: lastToInsert must be a later sibling of firstToInsert", "141": "Point.getNode() must return TextNode when type is text", "142": "Anchor node must be a TextNode", "143": "insertList: anchor should be defined", "144": "node is not a ListItemNode", "145": "focusEndRow is not a TableRowNode", "146": "Expected GridNode children to be TableRowNode", "147": "Expected TableRowNode children to be TableCellNode", "148": "Expected to find a parent TableCellNode", "149": "Expected TableCellNode to have a parent TableRowNode", "150": "Expected TableRowNode to have a parent GridNode", "151": "Expected TableSelection focus to be an ElementNode", "152": "Expected TableSelection anchor to be (or a child of) TableCellNode", "153": "getCellRect: expected to find AnchorNode", "154": "Expected TableSelection focus to be (or a child of) TableCellNode", "155": "getCellRect: expected to find focusCellNode", "156": "Expected anchorCell to have a parent TableRowNode", "157": "Expected tableNode to have a parent TableNode", "158": "Expected gridParent to have a parent", "159": "Expected focusCellParent to have a parent", "160": "Expected TableCellNode parent to be a TableRowNode", "161": "Unexpected dirty selection to be null", "162": "Root element not registered", "163": "node is not a ListNode", "164": "Root element count less than 0", "165": "%s should not be undefined. You may want to check splitOffsets passed to the splitText.", "166": "Expected valid LexicalSelection", "167": "Expected a valid Code Node: CodeHighlightNode, TabNode, LineBreakNode", "168": "Unexpected lineBreakNode in getEndOfCodeInLine", "169": "Expected selection to be inside CodeBlock and consisting of CodeHighlightNode, TabNode and LineBreakNode", "170": "Expected selection firstNode to be CodeHighlightNode or TabNode", "171": "Expected selection firstNode to be CodeHighlightNode or CodeTabNode", "172": "Expected getFirstCodeNodeOfLine to return a valid Code Node", "173": "MarkdownShortcuts: missing dependency %s for transformer. Ensure node dependency is included in editor initial config.", "174": "$createChildrenArray: node does not exist in nodeMap", "175": "Expected TableNode first child to be a RowNode", "176": "Expected TableNode cell to be a TableCellNode", "177": "isAtNodeEnd: node must be a TextNode or ElementNode", "178": "Expected block node to be an ElementNode", "179": "Expected node in emptyElements to be an ElementNode", "180": "Expected node be a TextNode", "181": "Expected text node to be first child of span", "182": "Unexpected null rootDOMNode", "183": "Unexpected null parentDOMNode", "184": "Element point must be an element node", "185": "%s doesn't extend the %s", "186": "Lexical node with constructor %s attempted to re-use key from node in active editor state with constructor %s. Keys must not be re-used when the type is changed.", "187": "Lexical node with constructor %s attempted to re-use key from node in active editor state with different constructor with the same name (possibly due to invalid Hot Module Replacement). Keys must not be re-used when the type is changed.", "188": "Expected a RangeSelection or TableSelection", "189": "Unable to find an active editor state. State helpers or node methods can only be used synchronously during the callback of editor.update(), editor.read(), or editorState.read().", "190": "Unable to find an active editor. This method can only be used synchronously during the callback of editor.update() or editor.read().", "191": "Unexpected empty pending editor state on discrete nested update", "192": "getCachedTypeToNodeMap called with a writable EditorState", "193": "$cloneWithProperties: %s.clone(node) (with type '%s') did not return a node with the same key, make sure to specify node.__key as the last argument to the constructor", "194": "Children of root nodes must be elements or decorators", "195": "Unable to find an active editor state. State helpers or node methods can only be used synchronously during the callback of editor.update(), editor.read(), or editorState.read().%s", "196": "Unable to find an active editor. This method can only be used synchronously during the callback of editor.update() or editor.read().%s", "197": "$cloneWithProperties: %s.clone(node) (with type '%s') overrode afterCloneFrom but did not call super.afterCloneFrom(prevNode)", "198": "Attempted to remove event handlers from a node that does not belong to this build of Lexical", "199": "Indent value must be non-negative.", "200": "$applyNodeReplacement node %s with type %s must be registered to the editor. You can do this by passing the node class via the \"nodes\" array in the editor config.", "201": "$applyNodeReplacement failed. Expected replacement node to be an instance of %s with type %s but returned %s with type %s from original node %s with type %s", "202": "$applyNodeReplacement failed. Ensure replacement node %s with type %s is a subclass of the original node %s with type %s.", "203": "$applyNodeReplacement failed. Ensure that the key argument is *not* used in your replace function (from node %s with type %s to node %s with type %s), Node keys must never be re-used except by the static clone method.", "204": "node is not a OverflowNode", "205": "tableElement already has an attached TableObserver", "206": "Expected TableNode childAtIndex(%s) to be RowNode", "207": "Anchor not found in Table", "208": "Focus not found in Table", "209": "Expected TableNode children to be TableRowNode", "210": "Expected TableRowNode to have a parent TableNode", "211": "Expected node %s of type %s to have a block ElementNode ancestor", "212": "Expected node %s of type %s to have a block ancestor", "213": "Expected ancestor to be a block ElementNode", "214": "$internalResolveSelectionPoint: node in DOM but not keyToDOMMap", "215": "$internalResolveSelectionPoint: resolvedElement is not an ElementNode", "216": "invariant", "217": "$validatePoint: %s key %s not found in current editorState", "218": "$validatePoint: %s key %s is not a TextNode", "219": "$validatePoint: %s point.offset > node.getTextContentSize() (%s > %s)", "220": "$validatePoint: %s key %s is not an ElementNode", "221": "$validatePoint: %s point.offset > node.getChildrenSize() (%s > %s)", "222": "ElementDOMSlot.insertChild: before is not in element", "223": "ElementDOMSlot.removeChild: dom is not in element", "224": "ElementDOMSlot.replaceChild: prevDom is not in element", "225": "indexPath: root is not a parent of child", "226": "ElementNode.splice: start + deleteCount > oldSize (%s + %s > %s)", "227": "$reconcileChildren: prevChildren.length !== prevChildrenSize", "228": "$reconcileChildren: nextChildren.length !== nextChildrenSize", "229": "TableNode.getDOMSlot: createDOM() did not return a table", "230": "$getElementForTableNode: Table Element Not Found", "231": "TableObserver: Expected tableNodeKey %s to be a TableNode", "232": "TableObserver: Expected to find TableElement in DOM for key %s", "233": "TableObserver.$updateTableTableSelection: selection.tableKey !== this.tableNodeKey ('%s' !== '%s')", "234": "TableObserver anchorTableCell is null", "235": "TableObserver focusTableCell is null", "236": "Expected Table selection", "237": "No table cells present", "238": "Expected TableSelection %s to be (or a child of) TableCellNode, got key %s of type %s", "239": "Expected TableSelection %s cell parent to be a TableRowNode", "240": "Expected TableSelection %s row parent to be a TableNode", "241": "Expected TableSelection anchor and focus to be in the same table", "242": "$createTableSelectionFrom: tableNode %s is not attached", "243": "$createTableSelectionFrom: anchorCell %s is not in table %s", "244": "$createTableSelectionFrom: focusCell %s is not in table %s", "245": "getTableElement: Expecting table in as DOM node for TableNode, not %s", "246": "applyTableHandlers: editor has no root element set", "247": "selectAdjacentCell: Cell not in table row", "248": "selectAdjacentCell: Row not in table", "249": "getCornerOrThrow: cell %s is not at a corner of rect", "250": "cellAtCornerOrThrow: %s = %s missing in tableMap", "251": "$handleArrowKey: TableSelection.getNodes()[0] expected to be TableNode", "252": "$getPointNode: element point is not an ElementNode", "253": "Expected TableSelection", "254": "TablePlugin: Expecting all children of TableNode to be TableRowNode, found %s (type %s)", "255": "TablePlugin: TableNode is not registered on editor", "256": "insertAfterEndRow is not a TableRowNode", "257": "insertBeforeStartRow is not a TableRowNode", "258": "$childIterator: Cycle detected, node with key %s has already been traversed", "259": "Point.getNode() must return ElementNode when type is element", "260": "PointType.set: node with key %s is %s and can not be used for a %s point", "261": "RangeSelection.getNodes() returned %s > 1 nodes in a collapsed selection", "262": "shouldDeleteExactlyOneCodeUnit: expecting to be called only with sequences of two or more code units", "263": "NodeCaret.splice: Underflow of expected nodesToRemove during splice (keys: %s)", "264": "$getTextPointCaret: invalid offset %s for size %s", "265": "$getCaretRange: anchor and focus must be in the same direction", "266": "$caretFromPoint: Node with type %s and key %s that does not inherit from TextNode encountered for text point", "267": "$caretFromPoint: Node with type %s and key %s that does not inherit from ElementNode encountered for element point", "268": "$setPointFromCaret: exhaustiveness check", "269": "$removeTextFromCaretRange: selection was lost, could not find a new anchor given candidates with keys: %s", "270": "$patchStyle must only be called with a TextNode or collapsed RangeSelection", "271": "$setState: State key collision %s detected in %s node with type %s and key %s. Only one StateConfig with a given key should be used on a node.", "272": "NodeState: size != computedSize (%s != %s)", "273": "NodeState: sharedConfigMap missing knownState key %s", "274": "$getTextNodeOffset: invalid offset %s for size %s", "275": "$comparePointCaretNext: a (key %s) and b (key %s) do not have a common ancestor", "276": "$originComparison: ancestor logic error", "277": "$originComparison: descendant logic error", "278": "$originComparison: branch logic error", "279": "LexicalNode.isBefore: exhaustiveness check", "280": "$patchStyle must only be called with a TextNode, ElementNode, or collapsed RangeSelection", "281": "$splitTextPointCaret: splitText must return at least one TextNode", "282": "rootNode.splice: Only element or decorator nodes can be inserted to the root node", "283": "$insertNodeToNearestRootAtCaret: An unattached TextNode can not be split", "284": "$getTextNodeOffset: invalid offset %s for size %s at key %s", "285": "$getCodeLines only extracts non-empty lines", "286": "TabNode does not support spliceText", "287": "Lexical node with constructor %s (type %s) has an incorrect clone implementation, got %s for nodeKey when expecting %s", "288": "%s (type %s) must implement a static clone method since its constructor has %s required arguments (expecting 0). Use an explicit default in the first argument of your constructor(prop: T=X, nodeKey?: NodeKey).", "289": "%s (type %s) must implement a static importJSON method since its constructor has %s required arguments (expecting 0). Use an explicit default in the first argument of your constructor(prop: T=X, nodeKey?: NodeKey)."}