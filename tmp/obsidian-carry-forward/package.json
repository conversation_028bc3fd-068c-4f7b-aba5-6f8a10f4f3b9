{"name": "obsidian-carry-forward", "version": "1.4.5", "description": "An Obsidian Notes plugin for generating and copying block IDs, and copying lines with a link to the copied line.", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "version": "node version-bump.mjs && git add manifest.json versions.json", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "test": "rm -rf test/empty_vault && yarn run build && mkdir -p test/empty_vault/.obsidian/plugins/sample-plugin && cp manifest.json styles.css main.js test/empty_vault/.obsidian/plugins/sample-plugin && npx wdio run wdio.conf.js"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/node": "^14.14.37", "@typescript-eslint/eslint-plugin": "^5.2.0", "@typescript-eslint/parser": "^5.2.0", "@wdio/cli": "^7.16.15", "@wdio/local-runner": "^7.16.15", "@wdio/mocha-framework": "^7.16.15", "@wdio/selenium-standalone-service": "^7.16.14", "@wdio/spec-reporter": "^7.16.14", "builtin-modules": "^3.2.0", "chai": "^4.3.6", "chromedriver": "^98.0.1", "esbuild": "0.13.12", "obsidian": "^0.12.0", "tslib": "^2.2.0", "typescript": "^4.2.4", "wdio-chromedriver-service": "^7.2.8"}}