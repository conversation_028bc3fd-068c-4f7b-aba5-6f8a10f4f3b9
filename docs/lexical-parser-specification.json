{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Lexical Parser Conversion Specification", "description": "JSON specification for bidirectional Obsidian Markdown ↔ Ghost Lexical conversion", "version": "1.0.0", "definitions": {"ParserConfig": {"type": "object", "properties": {"transformers": {"type": "array", "items": {"$ref": "#/definitions/Transformer"}, "description": "Array of transformers to use for conversion"}, "options": {"$ref": "#/definitions/ConversionOptions"}, "extensions": {"type": "array", "items": {"$ref": "#/definitions/Extension"}, "description": "Custom extensions for Ghost/Obsidian features"}}, "required": ["transformers"]}, "ConversionOptions": {"type": "object", "properties": {"preserveUnknownNodes": {"type": "boolean", "default": true, "description": "Whether to preserve unknown node types as fallback"}, "enableGhostFeatures": {"type": "boolean", "default": true, "description": "Enable Ghost-specific features (callouts, bookmarks, etc.)"}, "enableObsidianFeatures": {"type": "boolean", "default": true, "description": "Enable Obsidian-specific features (wikilinks, etc.)"}, "fallbackToHTML": {"type": "boolean", "default": true, "description": "Fallback to HTML conversion for unsupported content"}, "shouldPreserveNewLines": {"type": "boolean", "default": false, "description": "Preserve newlines in conversion"}, "shouldMergeAdjacentLines": {"type": "boolean", "default": false, "description": "Merge adjacent lines according to CommonMark spec"}, "performance": {"$ref": "#/definitions/PerformanceConfig"}}}, "PerformanceConfig": {"type": "object", "properties": {"batchSize": {"type": "integer", "default": 1000, "description": "Batch size for processing large documents"}, "useStringBuilder": {"type": "boolean", "default": true, "description": "Use optimized string building for large documents"}, "largeDocumentThreshold": {"type": "integer", "default": 50000, "description": "Threshold in bytes for considering a document large"}, "enableConverterCache": {"type": "boolean", "default": true, "description": "Enable caching of converter lookups"}}}, "Transformer": {"type": "object", "properties": {"type": {"type": "string", "enum": ["element", "multilineElement", "textFormat", "textMatch"], "description": "Type of transformer"}, "nodeType": {"type": "string", "description": "The node type this transformer handles"}, "priority": {"type": "integer", "default": 0, "description": "Priority for transformer ordering (higher = earlier)"}, "regExp": {"type": "string", "description": "Regular expression pattern for matching"}, "dependencies": {"type": "array", "items": {"type": "string"}, "description": "Required Lexical node types"}, "export": {"$ref": "#/definitions/ExportFunction"}, "import": {"$ref": "#/definitions/ImportFunction"}}, "required": ["type", "nodeType"]}, "Extension": {"type": "object", "properties": {"name": {"type": "string", "description": "Extension name"}, "platform": {"type": "string", "enum": ["ghost", "obsidian", "both"], "description": "Target platform for this extension"}, "transformers": {"type": "array", "items": {"$ref": "#/definitions/Transformer"}, "description": "Custom transformers for this extension"}, "enabled": {"type": "boolean", "default": true, "description": "Whether this extension is enabled"}}, "required": ["name", "platform", "transformers"]}, "ExportFunction": {"type": "object", "properties": {"handler": {"type": "string", "description": "Function name or identifier for export handling"}, "options": {"type": "object", "description": "Options specific to this export function"}}, "required": ["handler"]}, "ImportFunction": {"type": "object", "properties": {"handler": {"type": "string", "description": "Function name or identifier for import handling"}, "options": {"type": "object", "description": "Options specific to this import function"}}, "required": ["handler"]}, "ConversionResult": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Whether the conversion was successful"}, "data": {"oneOf": [{"$ref": "#/definitions/LexicalDocument"}, {"type": "string"}], "description": "Converted data (LexicalDocument for markdown->lexical, string for lexical->markdown)"}, "warnings": {"type": "array", "items": {"type": "string"}, "description": "Non-fatal warnings during conversion"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ConversionError"}, "description": "Errors that occurred during conversion"}, "metadata": {"$ref": "#/definitions/ConversionMetadata"}}, "required": ["success"]}, "ConversionError": {"type": "object", "properties": {"code": {"type": "string", "enum": ["NULL_INPUT", "INVALID_INPUT", "DOCUMENT_TOO_LARGE", "PARSE_FAILED", "CONVERSION_FAILED", "TRANSFORMER_ERROR", "UNEXPECTED_ERROR"]}, "message": {"type": "string", "description": "Human-readable error message"}, "details": {"type": "object", "description": "Additional error details"}, "stack": {"type": "string", "description": "Stack trace for debugging"}}, "required": ["code", "message"]}, "ConversionMetadata": {"type": "object", "properties": {"processingTime": {"type": "number", "description": "Time taken for conversion in milliseconds"}, "nodeCount": {"type": "integer", "description": "Number of nodes processed"}, "transformersUsed": {"type": "array", "items": {"type": "string"}, "description": "List of transformers that were used"}, "fallbacksUsed": {"type": "integer", "description": "Number of times fallback conversion was used"}}}, "LexicalDocument": {"type": "object", "properties": {"root": {"$ref": "#/definitions/LexicalRootNode"}}, "required": ["root"]}, "LexicalRootNode": {"type": "object", "properties": {"type": {"type": "string", "const": "root"}, "children": {"type": "array", "items": {"$ref": "#/definitions/LexicalNode"}}, "direction": {"type": "string", "enum": ["ltr", "rtl"], "default": "ltr"}, "format": {"type": "string", "default": ""}, "indent": {"type": "integer", "default": 0}, "version": {"type": "integer", "default": 1}}, "required": ["type", "children"]}, "LexicalNode": {"type": "object", "properties": {"type": {"type": "string", "description": "Node type identifier"}, "version": {"type": "integer", "default": 1}}, "required": ["type"], "additionalProperties": true}}, "standardTransformers": {"description": "Standard transformers based on official Lexical package", "element": [{"nodeType": "heading", "regExp": "^(#{1,6})\\s", "priority": 100}, {"nodeType": "quote", "regExp": "^>\\s", "priority": 90}, {"nodeType": "unorderedList", "regExp": "^(\\s*)[-*+]\\s", "priority": 80}, {"nodeType": "orderedList", "regExp": "^(\\s*)(\\d{1,})\\.\\s", "priority": 80}], "multilineElement": [{"nodeType": "code", "regExp": "^[ \\t]*```([\\w-]+)?", "priority": 110}], "textFormat": [{"nodeType": "inlineCode", "regExp": "`([^`]+)`", "priority": 120}, {"nodeType": "bold", "regExp": "\\*\\*([^*]+)\\*\\*", "priority": 100}, {"nodeType": "italic", "regExp": "\\*([^*]+)\\*", "priority": 90}, {"nodeType": "strikethrough", "regExp": "~~([^~]+)~~", "priority": 85}], "textMatch": [{"nodeType": "link", "regExp": "\\[([^\\]]+)\\]\\(([^)]+)\\)", "priority": 95}]}, "ghostExtensions": {"description": "Ghost-specific extensions", "transformers": [{"nodeType": "callout", "regExp": "^>\\s*\\[!([^\\]]+)\\]", "priority": 95, "platform": "ghost"}, {"nodeType": "bookmark", "regExp": "^\\[bookmark\\]\\(([^)]+)\\)", "priority": 85, "platform": "ghost"}, {"nodeType": "gallery", "regExp": "^\\[gallery\\]", "priority": 85, "platform": "ghost"}]}, "obsidianExtensions": {"description": "Obsidian-specific extensions", "transformers": [{"nodeType": "wikilink", "regExp": "\\[\\[([^\\]]+)\\]\\]", "priority": 100, "platform": "obsidian"}, {"nodeType": "mathBlock", "regExp": "^\\$\\$([\\s\\S]*?)\\$\\$", "priority": 105, "platform": "obsidian"}, {"nodeType": "mathInline", "regExp": "\\$([^$]+)\\$", "priority": 95, "platform": "obsidian"}]}}